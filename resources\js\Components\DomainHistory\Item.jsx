import React from "react";
import { Link, router } from "@inertiajs/react";
import Checkbox from "@/Components/Checkbox";
import Status from "./DomainStatusIndicators";
import { MdMoreVert } from "react-icons/md";
import DropDownContainer from "../DropDownContainer";
import { useState, useRef } from "react";
import useOutsideClick from "@/Util/useOutsideClick";
import Modal from "../Modal";
import SecondaryButton from "../SecondaryButton";
import DangerButton from "../DangerButton";
import { toast } from "react-toastify";

function Item({ domain, transactionTypes, isSelected, onCheckboxChange }) {
    const handleCheckboxChange = (e) => {
        const isChecked = e.target.checked;
        onCheckboxChange(domain.id, domain.is_active, isChecked);
    };
    const [confirmingUserDeletion, setConfirmingUserDeletion] = useState(false);
    const closeModal = () => {
        setConfirmingUserDeletion(false);
        setDomain("");
        setReason("");
        setErrors({});
        setInputValue("");
        setIsValid(false);
    };
    const [errors, setErrors] = useState({});
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    // console.log(domain);
    const [item, setDomain] = useState("");
    const [show, setShow] = useState(false);
    const [reason, setReason] = useState("");
    const [agreePolicy, setAgreePolicy] = useState(false);
    const [agreeGrace, setAgreeGrace] = useState(false);
    const [error, setError] = useState("");

    const handleConfirms = () => {
        if (!isValid) return;

        setSubmitting(true);
        setErrors({});
        handleDeleteDomainRequest({
            domainName: domain.name,
            userEmail: domain.domain_email,
            domainId: domain.id,
            createdDate: domain.created_at,
            userID: domain.userId,
            reason,
        });
    };

    function handleOnSubmit() {
        setConfirmingUserDeletion(true);
    }
    const handleDeleteDomainRequest = (itemReq) => {
        axios
            .post(route("domain.delete-request.delete"), {
                ...itemReq,
            })
            .then((response) => {
                // Clear fields
                setReason("");
                setErrors("");
                closeModal();
                setSubmitting(false);

                router.visit(
                    route("domain.delete-request.view") + "?statusType=APPROVED"
                );
                toast.success("Domain deletion finalized successfully.");

                return response;
            })
            .catch((error) => {
                if (error.response?.status === 422) {
                    setErrors(error.response.data.errors);
                    setShowConfirmStep(false); // go back to input if validation fails
                    setSubmitting(false);
                } else {
                    console.log("Something went wrong.");
                }
                return error.response;
            });
    };
    const [showConfirmStep, setShowConfirmStep] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputValue(value);
        setIsValid(
            value.trim().toLowerCase() === domain.domain_email.toLowerCase()
        );
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Clear previous error
        setErrors({});
        if (!reason.trim()) {
            setErrors({ reason: "Request reason is required." });
            return;
        }

        setShowConfirmStep(true);
    };
    return (
        <tr className="border-t mt-2">
            {/* {JSON.stringify(domain)} */}
            <td className="py-2 px-4 pt-4 pb-4">
                <label className="flex items-center space-x-2">
                    <div
                        style={{
                            visibility:
                                domain.status === "ACTIVE"
                                    ? "visible"
                                    : "hidden",
                        }}
                    >
                        <Checkbox
                            name="domain"
                            value={domain.id}
                            checked={isSelected}
                            handleChange={handleCheckboxChange}
                        />
                    </div>
                    <span>{domain.name}</span>
                </label>
            </td>
            <td className="py-2 px-4">
                {transactionTypes[domain.type] || "Unknown Type"}
            </td>
            <td className="py-2 px-4">{domain.domain_email}</td>
            <td className="py-2 px-4">
                {domain.created_at
                    ? new Date(
                          Date.parse(domain.created_at)
                      ).toLocaleDateString()
                    : "Invalid Date"}
            </td>
            <td className="py-2 px-4">
                <Status domain={domain} locked_until={domain.locked_until} />
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>

                    {domain.status !== "DELETED" && (
                        <DropDownContainer show={show}>
                            <button
                                className="px-5 py-1 justify-start flex"
                                onClick={handleOnSubmit}
                            >
                                Delete
                            </button>
                            <button
                                onClick={() =>
                                    router.visit(
                                        route("domain.history.show", {
                                            id: domain.id
                                        })
                                    )
                                }
                                className="px-5 py-1 justify-start flex text-left hover:bg-gray-100 w-full"
                            >
                                View Log
                            </button>
                        </DropDownContainer>
                    )}
                </span>
            </td>
            <td className="py-2 px-4">
                <Modal
                    show={confirmingUserDeletion}
                    onClose={closeModal}
                    className="p-6"
                >
                    <h1 className="text-4xl font-semibold text-black-900 border-b-1">
                        {domain.name}
                    </h1>
                    {!showConfirmStep ? (
                        <>
                            <div className="mt-4">
                                <label className="block text-lg font-medium text-black-700 mb-1">
                                    Your Message / Reason for Deletion{" "}
                                    <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    className="w-full border border-gray-300 rounded-md p-2"
                                    rows="3"
                                    value={reason}
                                    onChange={(e) => setReason(e.target.value)}
                                    placeholder="e.g., I no longer need this domain for my business..."
                                    name="reason"
                                ></textarea>
                                {errors.reason && (
                                    <p className="text-sm text-red-600 -mt-1">
                                        {errors.reason}
                                    </p>
                                )}
                            </div>
                            <div className="mt-6 flex justify-end items-end">
                                <div className="">
                                    <SecondaryButton onClick={closeModal}>
                                        Cancel
                                    </SecondaryButton>
                                    <DangerButton
                                        className="ml-3"
                                        onClick={handleSubmit}
                                    >
                                        {submitting ? "Deleting..." : "Delete"}
                                    </DangerButton>
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className="mt-4 mb-6 border border-yellow-200 bg-yellow-50 rounded p-4">
                            <p className="text-yellow-800 font-semibold mb-2">
                                Please type{" "}
                                <strong>{domain.domain_email}</strong> to
                                confirm deletion.
                            </p>
                            <input
                                type="text"
                                value={inputValue}
                                onChange={handleInputChange}
                                placeholder="Enter email address"
                                className="w-full border rounded px-4 py-2 mt-2 focus:ring-2 focus:ring-yellow-400"
                            />
                            <div className="flex justify-end gap-3 mt-4">
                                <button
                                    type="button"
                                    className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                                    onClick={() => setShowConfirmStep(false)}
                                >
                                    Back
                                </button>
                                <button
                                    type="button"
                                    onClick={handleConfirms}
                                    disabled={!isValid}
                                    className={`px-4 py-2 rounded text-white ${
                                        isValid
                                            ? "bg-green-600 hover:bg-green-700"
                                            : "bg-green-300 cursor-not-allowed"
                                    }`}
                                >
                                    {submitting ? "Deleting..." : "Confirm"}
                                </button>
                            </div>
                        </div>
                    )}
                </Modal>
            </td>
        </tr>
    );
}

export default Item;
